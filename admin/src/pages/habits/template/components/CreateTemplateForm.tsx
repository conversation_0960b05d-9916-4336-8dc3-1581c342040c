import React, { useState } from 'react';
import {
  ModalForm,
  ProFormText,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { message, Button, Space, Popover, ColorPicker } from 'antd';
import { useRequest } from '@umijs/max';
import { createHabitTemplate } from '@/services/habitTemplate';
import { getHabitCategories } from '@/services/habitCategory';
import type { CreateHabitTemplateDto } from '@/services/habitTemplate';
import type { HabitCategoryResponseDto } from '@/pages/habits/types/HabitCategoryResponseDto';

// 常用图标选项
const ICON_OPTIONS = [
  '🏃', '💪', '📚', '🎯', '⏰', '🧘', '🍎', '💧', '🛌', '🚿',
  '🦷', '🧴', '👕', '🧹', '📱', '💻', '🎵', '🎨', '✍️', '📝',
  '🌱', '🌟', '⭐', '🔥', '💎', '🏆', '🎉', '❤️', '💚', '💙',
];

interface CreateTemplateFormProps {
  trigger: React.ReactElement;
  onSuccess?: () => void;
}

const CreateTemplateForm: React.FC<CreateTemplateFormProps> = ({
  trigger,
  onSuccess,
}) => {
  const [selectedIcon, setSelectedIcon] = useState<string>('🎯');
  const [themeColor, setThemeColor] = useState<string>('#1677ff');
  const [messageApi, contextHolder] = message.useMessage();

  // 获取分类列表用于分类选择
  const { data: categoriesData, loading: categoriesLoading } = useRequest(
    () => getHabitCategories({ pageSize: 1000 }),
    {
      formatResult: (res) => res.data || [],
    }
  );

  // 创建模版
  const { run: createRun, loading: createLoading } = useRequest(createHabitTemplate, {
    manual: true,
    onSuccess: () => {
      messageApi.success('创建成功');
      onSuccess?.();
      return true;
    },
    onError: (error) => {
      messageApi.error(error.message || '创建失败，请重试');
    },
  });

  // 构建分类选项
  const categoryOptions = (categories: HabitCategoryResponseDto[]): any[] => {
    return categories.map(cat => ({
        label: `${cat.icon} ${cat.name}`,
        value: cat.id,
    }))
  }

  // 图标选择器
  const IconSelector = () => (
    <Popover
      content={
        <div style={{ width: 240 }}>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(8, 1fr)', gap: 8 }}>
            {ICON_OPTIONS.map((icon) => (
              <Button
                key={icon}
                size="small"
                type={selectedIcon === icon ? 'primary' : 'default'}
                onClick={() => setSelectedIcon(icon)}
                style={{ 
                  width: 24, 
                  height: 24, 
                  padding: 0,
                  fontSize: '14px',
                }}
              >
                {icon}
              </Button>
            ))}
          </div>
        </div>
      }
      title="选择图标"
      trigger="click"
    >
      <Button style={{ width: '100%', textAlign: 'left' }}>
        <Space>
          <span style={{ fontSize: '16px' }}>{selectedIcon}</span>
          <span>点击选择图标</span>
        </Space>
      </Button>
    </Popover>
  );

  return (
    <>
      {contextHolder}
      <ModalForm<CreateHabitTemplateDto>
        title="新建模版"
        trigger={trigger}
        width={500}
        layout="horizontal"
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        modalProps={{
          destroyOnClose: true,
        }}
        onFinish={async (values) => {
          await createRun({
            ...values,
            icon: selectedIcon,
            theme_color: themeColor,
          });
          return true;
        }}
        loading={createLoading}
      >
        <ProFormText
          name="name"
          label="模版名称"
          placeholder="请输入模版名称"
          rules={[
            { required: true, message: '请输入模版名称' },
            { max: 50, message: '模版名称不能超过50个字符' },
          ]}
        />

        <ProFormSelect
          name="category_id"
          label="所属分类"
          placeholder="请选择所属分类"
          options={categoriesData ? categoryOptions(categoriesData) : []}
          fieldProps={{
            loading: categoriesLoading,
          }}
          rules={[{ required: true, message: '请选择所属分类' }]}
          showSearch
        />
        
        <div style={{ marginBottom: 24 }}>
          <div style={{ marginBottom: 8 }}>
            <span style={{ color: '#ff4d4f' }}>*</span>
            <span style={{ marginLeft: 4 }}>模版图标</span>
          </div>
          <IconSelector />
        </div>

        <div style={{ marginBottom: 24 }}>
            <div style={{ marginBottom: 8 }}>
                <span style={{ color: '#ff4d4f' }}>*</span>
                <span style={{ marginLeft: 4 }}>主题色</span>
            </div>
            <ColorPicker value={themeColor} onChange={(c) => setThemeColor(c.toHexString())} />
        </div>

        <ProFormTextArea
          name="description"
          label="模版描述"
          placeholder="请输入模版描述"
          rules={[
            { max: 200, message: '模版描述不能超过200个字符' },
          ]}
        />
      </ModalForm>
    </>
  );
};

export default CreateTemplateForm;

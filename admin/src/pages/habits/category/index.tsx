import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  PageContainer,
  ProTable,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import {
  Button,
  message,
  Popconfirm,
  Switch,
  Tag,
  Space,
  Typography,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DragOutlined
} from '@ant-design/icons';
import React, { useRef, useState } from 'react';
import {
  getHabitCategories,
  deleteHabitCategory,
  batchDeleteHabitCategories,
  toggleHabitCategoryStatus,
} from '@/services/habitCategory';
import type { HabitCategoryResponseDto } from '@/pages/habits/types/HabitCategoryResponseDto';
import CreateCategoryForm from './components/CreateCategoryForm';
import UpdateCategoryForm from './components/UpdateCategoryForm';

const { Text } = Typography;

const HabitCategory: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [selectedRowsState, setSelectedRows] = useState<HabitCategoryResponseDto[]>([]);
  const [messageApi, contextHolder] = message.useMessage();

  // 删除分类
  const { run: deleteRun, loading: deleteLoading } = useRequest<any, [string]>((id) => deleteHabitCategory(id), {
    manual: true,
    onSuccess: () => {
      messageApi.success('删除成功');
      actionRef.current?.reload();
    },
    onError: (error) => {
      messageApi.error(error.message || '删除失败，请重试');
    },
  });

  // 批量删除分类
  const { run: batchDeleteRun, loading: batchDeleteLoading } = useRequest<any, [string[]]>((ids) => batchDeleteHabitCategories(ids), {
    manual: true,
    onSuccess: () => {
      messageApi.success('批量删除成功');
      setSelectedRows([]);
      actionRef.current?.reload();
    },
    onError: (error) => {
      messageApi.error(error.message || '批量删除失败，请重试');
    },
  });

  // 切换状态
  const { run: toggleStatusRun } = useRequest<any, [string, boolean]>((id, is_active) => toggleHabitCategoryStatus(id, is_active), {
    manual: true,
    onSuccess: () => {
      messageApi.success('状态更新成功');
      actionRef.current?.reload();
    },
    onError: (error) => {
      messageApi.error(error.message || '状态更新失败，请重试');
    },
  });

  // 获取层级标签颜色
  const getLevelColor = (level: number) => {
    const colors = ['blue', 'green', 'orange', 'purple', 'red'];
    return colors[level % colors.length];
  };

  // 渲染分类名称（带层级缩进）
  const renderCategoryName = (record: HabitCategoryResponseDto) => {
    const indent = record.level * 20;
    return (
      <div style={{ display: 'flex', alignItems: 'center', paddingLeft: indent }}>
        <span style={{ fontSize: '16px', marginRight: 8 }}>{record.icon}</span>
        <Text strong={record.level === 0}>{record.name}</Text>
        <Tag
          color={getLevelColor(record.level)}
          style={{ marginLeft: 8 }}
        >
          L{record.level}
        </Tag>
        {record.template_count !== undefined && (
          <Tag color="cyan" style={{ marginLeft: 4 }}>
            {record.template_count}个模板
          </Tag>
        )}
      </div>
    );
  };

  const columns: ProColumns<HabitCategoryResponseDto>[] = [
    {
      title: '排序',
      dataIndex: 'sort_order',
      width: 80,
      hideInSearch: true,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <DragOutlined style={{ color: '#999', cursor: 'move' }} />
          <Text type="secondary">{record.sort_order}</Text>
        </div>
      ),
    },
    {
      title: '分类名称',
      dataIndex: 'name',
      width: 300,
      render: (_, record) => renderCategoryName(record),
    },
    {
      title: '父分类',
      dataIndex: 'parent_id',
      width: 150,
      hideInSearch: true,
      render: (_, record) => {
        if (!record.parent_id) {
          return <Tag color="blue">根分类</Tag>;
        }
        return <Text type="secondary">-</Text>;
      },
    },
    // {
    //   title: '层级',
    //   dataIndex: 'level',
    //   width: 80,
    //   valueType: 'select',
    //   valueEnum: {
    //     0: { text: '一级' },
    //     1: { text: '二级' },
    //     2: { text: '三级' },
    //     3: { text: '四级' },
    //   },
    //   render: (_, record) => (
    //     <Tag color={getLevelColor(record.level)}>
    //       第{record.level + 1}级
    //     </Tag>
    //   ),
    // },
    {
      title: '状态',
      dataIndex: 'is_active',
      width: 100,
      valueType: 'select',
      valueEnum: {
        true: { text: '启用', status: 'Success' },
        false: { text: '禁用', status: 'Default' },
      },
      render: (_, record) => (
        <Switch
          checked={record.is_active}
          onChange={(checked) => toggleStatusRun(record.id, checked)}
          size="small"
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      width: 160,
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      width: 160,
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 180,
      render: (_, record) => [
        <UpdateCategoryForm
          key="edit"
          trigger={
            <Button
              type="link"
              icon={<EditOutlined />}
              size="small"
            >
              编辑
            </Button>
          }
          values={record}
          onSuccess={() => actionRef.current?.reload()}
        />,
        <Popconfirm
          key="delete"
          title="确定要删除这个分类吗？"
          description={
            record.children && record.children.length > 0
              ? "该分类下还有子分类，删除后子分类也会被删除"
              : record.template_count && record.template_count > 0
                ? `该分类下还有 ${record.template_count} 个模板，请先处理相关模板`
                : "删除后不可恢复"
          }
          onConfirm={() => deleteRun(record.id)}
          okText="确定"
          cancelText="取消"
          disabled={!!(record.template_count && record.template_count > 0)}
        >
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            size="small"
            disabled={!!(record.template_count && record.template_count > 0)}
          >
            {record.template_count && record.template_count > 0 ? '不可删除' : '删除'}
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      {contextHolder}
      <ProTable<HabitCategoryResponseDto>
        // headerTitle="习惯分类管理"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <CreateCategoryForm
            key="create"
            trigger={
              <Button type="primary" icon={<PlusOutlined />}>
                新建分类
              </Button>
            }
            onSuccess={() => actionRef.current?.reload()}
          />,
        ]}
        request={async (params) => {
          const response = await getHabitCategories({
            current: params.current,
            pageSize: params.pageSize,
            name: params.name,
            level: params.level,
            is_active: params.is_active === 'true' ? true : params.is_active === 'false' ? false : undefined,
          });

          return {
            data: response.data || [],
            success: response.success,
            total: response.total || 0,
          };
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => ({
            disabled: !!(record.template_count && record.template_count > 0),
          }),
        }}
        pagination={{
          defaultPageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        options={{
          reload: true,
          density: true,
          fullScreen: true,
        }}
      />

      {selectedRowsState?.length > 0 && (
        <div
          style={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            background: '#fff',
            borderTop: '1px solid #f0f0f0',
            padding: '16px 24px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            zIndex: 1000,
          }}
        >
          <div>
            已选择 <Text strong>{selectedRowsState.length}</Text> 项
          </div>
          <Space>
            <Button onClick={() => setSelectedRows([])}>
              取消选择
            </Button>
            <Popconfirm
              title={`确定要删除选中的 ${selectedRowsState.length} 个分类吗？`}
              description="删除后不可恢复，请谨慎操作"
              onConfirm={() => batchDeleteRun(selectedRowsState.map(row => row.id))}
              okText="确定"
              cancelText="取消"
            >
              <Button
                danger
                loading={batchDeleteLoading || deleteLoading}
                icon={<DeleteOutlined />}
              >
                批量删除
              </Button>
            </Popconfirm>
          </Space>
        </div>
      )}
    </PageContainer>
  );
};

export default HabitCategory;
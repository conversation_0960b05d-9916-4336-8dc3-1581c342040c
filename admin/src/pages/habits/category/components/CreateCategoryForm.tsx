import React, { useState } from 'react';
import {
  ModalForm,
  ProFormText,
  ProFormSelect,
  ProFormDigit,
} from '@ant-design/pro-components';
import { message, Button, Space, Popover } from 'antd';
import { useRequest } from '@umijs/max';
import { createHabitCategory, getHabitCategories } from '@/services/habitCategory';
import type { CreateHabitCategoryDto } from '@/services/habitCategory';
import type { HabitCategoryResponseDto } from '@/pages/habits/types/HabitCategoryResponseDto';

// 常用图标选项
const ICON_OPTIONS = [
  '🏃', '💪', '📚', '🎯', '⏰', '🧘', '🍎', '💧', '🛌', '🚿',
  '🦷', '🧴', '👕', '🧹', '📱', '💻', '🎵', '🎨', '✍️', '📝',
  '🌱', '🌟', '⭐', '🔥', '💎', '🏆', '🎉', '❤️', '💚', '💙',
];

interface CreateCategoryFormProps {
  trigger: React.ReactElement;
  onSuccess?: () => void;
}

const CreateCategoryForm: React.FC<CreateCategoryFormProps> = ({
  trigger,
  onSuccess,
}) => {
  const [selectedIcon, setSelectedIcon] = useState<string>('🎯');
  const [messageApi, contextHolder] = message.useMessage();

  // 获取分类列表用于父分类选择
  const { data: categoriesData, loading: categoriesLoading } = useRequest(
    () => getHabitCategories({ pageSize: 1000 }),
    {
      formatResult: (res) => res.data || [],
    }
  );

  // 创建分类
  const { run: createRun, loading: createLoading } = useRequest(createHabitCategory, {
    manual: true,
    onSuccess: () => {
      messageApi.success('创建成功');
      onSuccess?.();
    },
    onError: (error) => {
      messageApi.error(error.message || '创建失败，请重试');
    },
  });

  // 构建父分类选项（树形结构）
  const buildParentOptions = (categories: HabitCategoryResponseDto[], level = 0): any[] => {
    const options: any[] = [];
    
    categories
      .filter(cat => cat.level === level)
      .sort((a, b) => a.sort_order - b.sort_order)
      .forEach(category => {
        options.push({
          label: `${'  '.repeat(level)}${category.icon} ${category.name}`,
          value: category.id,
          disabled: level >= 3, // 限制最大层级为4级
        });
        
        // 递归添加子分类
        const children = buildParentOptions(categories, level + 1);
        options.push(...children);
      });
    
    return options;
  };

  const parentOptions = categoriesData ? buildParentOptions(categoriesData) : [];

  // 图标选择器
  const IconSelector = () => (
    <Popover
      content={
        <div style={{ width: 240 }}>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(8, 1fr)', gap: 8 }}>
            {ICON_OPTIONS.map((icon) => (
              <Button
                key={icon}
                size="small"
                type={selectedIcon === icon ? 'primary' : 'default'}
                onClick={() => setSelectedIcon(icon)}
                style={{ 
                  width: 24, 
                  height: 24, 
                  padding: 0,
                  fontSize: '14px',
                }}
              >
                {icon}
              </Button>
            ))}
          </div>
        </div>
      }
      title="选择图标"
      trigger="click"
    >
      <Button style={{ width: '100%', textAlign: 'left' }}>
        <Space>
          <span style={{ fontSize: '16px' }}>{selectedIcon}</span>
          <span>点击选择图标</span>
        </Space>
      </Button>
    </Popover>
  );

  return (
    <>
      {contextHolder}
      <ModalForm<CreateHabitCategoryDto>
        title="新建分类"
        trigger={trigger}
        width={500}
        layout="horizontal"
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        modalProps={{
          destroyOnClose: true,
        }}
        onFinish={async (values) => {
          await createRun({
            ...values,
            icon: selectedIcon,
          });
          return true;
        }}
        loading={createLoading}
      >
        <ProFormText
          name="name"
          label="分类名称"
          placeholder="请输入分类名称"
          rules={[
            { required: true, message: '请输入分类名称' },
            { max: 50, message: '分类名称不能超过50个字符' },
          ]}
        />
        
        <div style={{ marginBottom: 24 }}>
          <div style={{ marginBottom: 8 }}>
            <span style={{ color: '#ff4d4f' }}>*</span>
            <span style={{ marginLeft: 4 }}>分类图标</span>
          </div>
          <IconSelector />
        </div>

        <ProFormSelect
          name="parent_id"
          label="父分类"
          placeholder="请选择父分类（可选）"
          options={[
            { label: '无（作为根分类）', value: undefined },
            ...parentOptions,
          ]}
          fieldProps={{
            loading: categoriesLoading,
            filterOption: (input, option: any) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
          }}
        />

        <ProFormDigit
          name="sort_order"
          label="排序值"
          placeholder="请输入排序值"
          min={0}
          max={9999}
          fieldProps={{
            precision: 0,
          }}
          extra="数值越小排序越靠前，默认为0"
        />
      </ModalForm>
    </>
  );
};

export default CreateCategoryForm;
import React, { useState, useEffect } from 'react';
import {
  ModalForm,
  ProFormText,
  ProFormSelect,
  ProFormDigit,
} from '@ant-design/pro-components';
import { message, Button, Space, Popover } from 'antd';
import { useRequest } from '@umijs/max';
import { updateHabitCategory, getHabitCategories } from '@/services/habitCategory';
import type { UpdateHabitCategoryDto } from '@/services/habitCategory';
import type { HabitCategoryResponseDto } from '@/pages/habits/types/HabitCategoryResponseDto';

// 常用图标选项
const ICON_OPTIONS = [
  '🏃', '💪', '📚', '🎯', '⏰', '🧘', '🍎', '💧', '🛌', '🚿',
  '🦷', '🧴', '👕', '🧹', '📱', '💻', '🎵', '🎨', '✍️', '📝',
  '🌱', '🌟', '⭐', '🔥', '💎', '🏆', '🎉', '❤️', '💚', '💙',
];

interface UpdateCategoryFormProps {
  trigger: React.ReactElement;
  values: HabitCategoryResponseDto;
  onSuccess?: () => void;
}

const UpdateCategoryForm: React.FC<UpdateCategoryFormProps> = ({
  trigger,
  values,
  onSuccess,
}) => {
  const [selectedIcon, setSelectedIcon] = useState<string>(values.icon);
  const [messageApi, contextHolder] = message.useMessage();

  // 重置图标选择
  useEffect(() => {
    setSelectedIcon(values.icon);
  }, [values.icon]);

  // 获取分类列表用于父分类选择
  const { data: categoriesData, loading: categoriesLoading } = useRequest(
    () => getHabitCategories({ pageSize: 1000 }),
    {
      formatResult: (res) => res.data || [],
    }
  );

  // 更新分类
  const { run: updateRun, loading: updateLoading } = useRequest(updateHabitCategory, {
    manual: true,
    onSuccess: () => {
      messageApi.success('更新成功');
      onSuccess?.();
    },
    onError: (error) => {
      messageApi.error(error.message || '更新失败，请重试');
    },
  });

  // 获取所有子分类ID（递归）
  const getAllChildrenIds = (categoryId: string, categories: HabitCategoryResponseDto[]): string[] => {
    const childIds: string[] = [];
    const children = categories.filter(cat => cat.parent_id === categoryId);
    
    children.forEach(child => {
      childIds.push(child.id);
      childIds.push(...getAllChildrenIds(child.id, categories));
    });
    
    return childIds;
  };

  // 构建父分类选项（排除自己和子分类）
  const buildParentOptions = (categories: HabitCategoryResponseDto[], level = 0): any[] => {
    const options: any[] = [];
    const excludeIds = [values.id, ...getAllChildrenIds(values.id, categories)];
    
    categories
      .filter(cat => cat.level === level && !excludeIds.includes(cat.id))
      .sort((a, b) => a.sort_order - b.sort_order)
      .forEach(category => {
        // 检查层级限制：如果选择这个父分类，当前分类的层级不能超过3
        const wouldExceedLevel = category.level >= 3;
        
        options.push({
          label: `${'  '.repeat(level)}${category.icon} ${category.name}`,
          value: category.id,
          disabled: wouldExceedLevel,
        });
        
        // 递归添加子分类
        const children = buildParentOptions(categories, level + 1);
        options.push(...children);
      });
    
    return options;
  };

  const parentOptions = categoriesData ? buildParentOptions(categoriesData) : [];

  // 图标选择器
  const IconSelector = () => (
    <Popover
      content={
        <div style={{ width: 240 }}>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(8, 1fr)', gap: 8 }}>
            {ICON_OPTIONS.map((icon) => (
              <Button
                key={icon}
                size="small"
                type={selectedIcon === icon ? 'primary' : 'default'}
                onClick={() => setSelectedIcon(icon)}
                style={{ 
                  width: 24, 
                  height: 24, 
                  padding: 0,
                  fontSize: '14px',
                }}
              >
                {icon}
              </Button>
            ))}
          </div>
        </div>
      }
      title="选择图标"
      trigger="click"
    >
      <Button style={{ width: '100%', textAlign: 'left' }}>
        <Space>
          <span style={{ fontSize: '16px' }}>{selectedIcon}</span>
          <span>点击选择图标</span>
        </Space>
      </Button>
    </Popover>
  );

  return (
    <>
      {contextHolder}
      <ModalForm<UpdateHabitCategoryDto>
        title="编辑分类"
        trigger={trigger}
        width={500}
        layout="horizontal"
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        modalProps={{
          destroyOnClose: true,
        }}
        initialValues={{
          name: values.name,
          parent_id: values.parent_id,
          sort_order: values.sort_order,
        }}
        onFinish={async (formValues) => {
          await updateRun(values.id, {
            ...formValues,
            icon: selectedIcon,
          });
          return true;
        }}
        loading={updateLoading}
      >
        <ProFormText
          name="name"
          label="分类名称"
          placeholder="请输入分类名称"
          rules={[
            { required: true, message: '请输入分类名称' },
            { max: 50, message: '分类名称不能超过50个字符' },
          ]}
        />
        
        <div style={{ marginBottom: 24 }}>
          <div style={{ marginBottom: 8 }}>
            <span style={{ color: '#ff4d4f' }}>*</span>
            <span style={{ marginLeft: 4 }}>分类图标</span>
          </div>
          <IconSelector />
        </div>

        <ProFormSelect
          name="parent_id"
          label="父分类"
          placeholder="请选择父分类（可选）"
          options={[
            { label: '无（作为根分类）', value: undefined },
            ...parentOptions,
          ]}
          fieldProps={{
            loading: categoriesLoading,
            filterOption: (input, option: any) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
          }}
          extra={values.children && values.children.length > 0 ? 
            `注意：该分类下有 ${values.children.length} 个子分类，修改父分类可能影响层级结构` : 
            undefined
          }
        />

        <ProFormDigit
          name="sort_order"
          label="排序值"
          placeholder="请输入排序值"
          min={0}
          max={9999}
          fieldProps={{
            precision: 0,
          }}
          extra="数值越小排序越靠前"
        />
      </ModalForm>
    </>
  );
};

export default UpdateCategoryForm;
import { Test, TestingModule } from '@nestjs/testing';
import { HttpStatus } from '@nestjs/common';
import { HabitCategoryService } from '../habit-category.service';
import { HabitCategoryRepository } from '../../repositories/habit-category.repository';
import { BusinessException } from '../../../common/exceptions/business.exception';
import { ErrorCode } from '../../../common/enums';
import { IHabitCategory } from '../../interfaces/habit-category.interface';
import { CreateHabitCategoryDto } from '../../dto/habit-category/create-habit-category.dto';
import { UpdateHabitCategoryDto } from '../../dto/habit-category/update-habit-category.dto';
import { HabitCategoryQueryDto } from '../../dto/habit-category/habit-category-query.dto';

describe('HabitCategoryService', () => {
  let service: HabitCategoryService;
  let habitCategoryRepository: jest.Mocked<HabitCategoryRepository>;

  const mockCategory: IHabitCategory = {
    id: 'category-1',
    name: '生活自理',
    icon: 'life-icon',
    sort_order: 1,
    is_active: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
  };

  const mockCategory2: IHabitCategory = {
    id: 'category-2',
    name: '健康习惯',
    icon: 'health-icon',
    sort_order: 2,
    is_active: true,
    created_at: new Date('2024-01-02'),
    updated_at: new Date('2024-01-02'),
  };

  const mockInactiveCategory: IHabitCategory = {
    id: 'category-3',
    name: '已停用分类',
    icon: 'inactive-icon',
    sort_order: 3,
    is_active: false,
    created_at: new Date('2024-01-03'),
    updated_at: new Date('2024-01-03'),
  };

  beforeEach(async () => {
    const mockHabitCategoryRepository = {
      findActiveCategories: jest.fn(),
      findAllCategories: jest.fn(),
      findById: jest.fn(),
      findByName: jest.fn(),
      createCategory: jest.fn(),
      updateCategory: jest.fn(),
      delete: jest.fn(),
      hasTemplates: jest.fn(),
      getNextSortOrder: jest.fn(),
      reorderCategories: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HabitCategoryService,
        {
          provide: HabitCategoryRepository,
          useValue: mockHabitCategoryRepository,
        },
      ],
    }).compile();

    service = module.get<HabitCategoryService>(HabitCategoryService);
    habitCategoryRepository = module.get(HabitCategoryRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findAll', () => {
    it('should return active categories by default', async () => {
      const query: HabitCategoryQueryDto = {};
      habitCategoryRepository.findActiveCategories.mockResolvedValue([
        mockCategory,
        mockCategory2,
      ]);

      const result = await service.findAll(query);

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe(mockCategory.id);
      expect(result[1].id).toBe(mockCategory2.id);
      expect(habitCategoryRepository.findActiveCategories).toHaveBeenCalled();
      expect(habitCategoryRepository.findAllCategories).not.toHaveBeenCalled();
    });

    it('should return all categories when is_active is false', async () => {
      const query: HabitCategoryQueryDto = { is_active: false };
      habitCategoryRepository.findAllCategories.mockResolvedValue([
        mockCategory,
        mockCategory2,
        mockInactiveCategory,
      ]);

      const result = await service.findAll(query);

      expect(result).toHaveLength(3);
      expect(habitCategoryRepository.findAllCategories).toHaveBeenCalled();
      expect(
        habitCategoryRepository.findActiveCategories,
      ).not.toHaveBeenCalled();
    });
  });

  describe('findById', () => {
    it('should return category when found', async () => {
      habitCategoryRepository.findById.mockResolvedValue(mockCategory);

      const result = await service.findById(mockCategory.id);

      expect(result.id).toBe(mockCategory.id);
      expect(result.name).toBe(mockCategory.name);
      expect(habitCategoryRepository.findById).toHaveBeenCalledWith(
        mockCategory.id,
      );
    });

    it('should throw BusinessException when category not found', async () => {
      habitCategoryRepository.findById.mockResolvedValue(null);

      await expect(service.findById('non-existent-id')).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_CATEGORY_NOT_FOUND,
          '习惯分类不存在',
          HttpStatus.NOT_FOUND,
        ),
      );
    });
  });

  describe('create', () => {
    const createDto: CreateHabitCategoryDto = {
      name: '新分类',
      icon: 'new-icon',
      sort_order: 5,
      is_active: true,
    };

    it('should create category successfully', async () => {
      habitCategoryRepository.findByName.mockResolvedValue(null);
      habitCategoryRepository.createCategory.mockResolvedValue({
        ...mockCategory,
        name: createDto.name,
        icon: createDto.icon,
        sort_order: createDto.sort_order!,
      });

      const result = await service.create(createDto);

      expect(result.name).toBe(createDto.name);
      expect(result.icon).toBe(createDto.icon);
      expect(result.sort_order).toBe(createDto.sort_order);
      expect(habitCategoryRepository.findByName).toHaveBeenCalledWith(
        createDto.name,
      );
      expect(habitCategoryRepository.createCategory).toHaveBeenCalledWith({
        name: createDto.name,
        icon: createDto.icon,
        sort_order: createDto.sort_order,
        is_active: true,
      });
    });

    it('should auto-assign sort order when not provided', async () => {
      const createDtoWithoutSort = { ...createDto };
      delete createDtoWithoutSort.sort_order;

      habitCategoryRepository.findByName.mockResolvedValue(null);
      habitCategoryRepository.getNextSortOrder.mockResolvedValue(10);
      habitCategoryRepository.createCategory.mockResolvedValue({
        ...mockCategory,
        name: createDto.name,
        sort_order: 10,
      });

      const result = await service.create(createDtoWithoutSort);

      expect(result.sort_order).toBe(10);
      expect(habitCategoryRepository.getNextSortOrder).toHaveBeenCalled();
      expect(habitCategoryRepository.createCategory).toHaveBeenCalledWith({
        name: createDto.name,
        icon: createDto.icon,
        sort_order: 10,
        is_active: true,
      });
    });

    it('should throw BusinessException when category name already exists', async () => {
      habitCategoryRepository.findByName.mockResolvedValue(mockCategory);

      await expect(service.create(createDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_CATEGORY_NAME_EXISTS,
          '分类名称已存在',
          HttpStatus.CONFLICT,
        ),
      );
    });
  });

  describe('update', () => {
    const updateDto: UpdateHabitCategoryDto = {
      name: '更新后的分类',
      icon: 'updated-icon',
      sort_order: 10,
    };

    it('should update category successfully', async () => {
      const updatedCategory = { ...mockCategory, ...updateDto };
      habitCategoryRepository.findById.mockResolvedValue(mockCategory);
      habitCategoryRepository.findByName.mockResolvedValue(null);
      habitCategoryRepository.updateCategory.mockResolvedValue(updatedCategory);

      const result = await service.update(mockCategory.id, updateDto);

      expect(result.name).toBe(updateDto.name);
      expect(result.icon).toBe(updateDto.icon);
      expect(result.sort_order).toBe(updateDto.sort_order);
      expect(habitCategoryRepository.findById).toHaveBeenCalledWith(
        mockCategory.id,
      );
      expect(habitCategoryRepository.updateCategory).toHaveBeenCalledWith(
        mockCategory.id,
        updateDto,
      );
    });

    it('should throw BusinessException when category not found', async () => {
      habitCategoryRepository.findById.mockResolvedValue(null);

      await expect(
        service.update('non-existent-id', updateDto),
      ).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_CATEGORY_NOT_FOUND,
          '习惯分类不存在',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should throw BusinessException when updated name already exists', async () => {
      habitCategoryRepository.findById.mockResolvedValue(mockCategory);
      habitCategoryRepository.findByName.mockResolvedValue(mockCategory2);

      await expect(service.update(mockCategory.id, updateDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_CATEGORY_NAME_EXISTS,
          '分类名称已存在',
          HttpStatus.CONFLICT,
        ),
      );
    });

    it('should allow updating with same name', async () => {
      const updateDtoSameName = { ...updateDto, name: mockCategory.name };
      const updatedCategory = { ...mockCategory, ...updateDtoSameName };

      habitCategoryRepository.findById.mockResolvedValue(mockCategory);
      habitCategoryRepository.updateCategory.mockResolvedValue(updatedCategory);

      const result = await service.update(mockCategory.id, updateDtoSameName);

      expect(result.name).toBe(mockCategory.name);
      expect(habitCategoryRepository.findByName).not.toHaveBeenCalled();
    });
  });

  describe('delete', () => {
    it('should delete category successfully when no templates exist', async () => {
      habitCategoryRepository.findById.mockResolvedValue(mockCategory);
      habitCategoryRepository.hasTemplates.mockResolvedValue(false);
      habitCategoryRepository.delete.mockResolvedValue(true);

      await service.delete(mockCategory.id);

      expect(habitCategoryRepository.findById).toHaveBeenCalledWith(
        mockCategory.id,
      );
      expect(habitCategoryRepository.hasTemplates).toHaveBeenCalledWith(
        mockCategory.id,
      );
      expect(habitCategoryRepository.delete).toHaveBeenCalledWith(
        mockCategory.id,
      );
    });

    it('should throw BusinessException when category not found', async () => {
      habitCategoryRepository.findById.mockResolvedValue(null);

      await expect(service.delete('non-existent-id')).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_CATEGORY_NOT_FOUND,
          '习惯分类不存在',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should throw BusinessException when category has templates', async () => {
      habitCategoryRepository.findById.mockResolvedValue(mockCategory);
      habitCategoryRepository.hasTemplates.mockResolvedValue(true);

      await expect(service.delete(mockCategory.id)).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_CATEGORY_HAS_TEMPLATES,
          '分类下存在习惯模板，无法删除',
          HttpStatus.BAD_REQUEST,
        ),
      );
    });
  });

  describe('activate', () => {
    it('should activate category successfully', async () => {
      const activatedCategory = { ...mockInactiveCategory, is_active: true };
      habitCategoryRepository.findById.mockResolvedValue(mockInactiveCategory);
      habitCategoryRepository.updateCategory.mockResolvedValue(
        activatedCategory,
      );

      const result = await service.activate(mockInactiveCategory.id);

      expect(result.is_active).toBe(true);
      expect(habitCategoryRepository.findById).toHaveBeenCalledWith(
        mockInactiveCategory.id,
      );
      expect(habitCategoryRepository.updateCategory).toHaveBeenCalledWith(
        mockInactiveCategory.id,
        { is_active: true },
      );
    });

    it('should throw BusinessException when category not found', async () => {
      habitCategoryRepository.findById.mockResolvedValue(null);

      await expect(service.activate('non-existent-id')).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_CATEGORY_NOT_FOUND,
          '习惯分类不存在',
          HttpStatus.NOT_FOUND,
        ),
      );
    });
  });

  describe('deactivate', () => {
    it('should deactivate category successfully', async () => {
      const deactivatedCategory = { ...mockCategory, is_active: false };
      habitCategoryRepository.findById.mockResolvedValue(mockCategory);
      habitCategoryRepository.updateCategory.mockResolvedValue(
        deactivatedCategory,
      );

      const result = await service.deactivate(mockCategory.id);

      expect(result.is_active).toBe(false);
      expect(habitCategoryRepository.findById).toHaveBeenCalledWith(
        mockCategory.id,
      );
      expect(habitCategoryRepository.updateCategory).toHaveBeenCalledWith(
        mockCategory.id,
        { is_active: false },
      );
    });

    it('should throw BusinessException when category not found', async () => {
      habitCategoryRepository.findById.mockResolvedValue(null);

      await expect(service.deactivate('non-existent-id')).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_CATEGORY_NOT_FOUND,
          '习惯分类不存在',
          HttpStatus.NOT_FOUND,
        ),
      );
    });
  });

  describe('reorderCategories', () => {
    const categoryOrders = [
      { id: 'category-1', sort_order: 2 },
      { id: 'category-2', sort_order: 1 },
    ];

    it('should reorder categories successfully', async () => {
      habitCategoryRepository.findById
        .mockResolvedValueOnce(mockCategory)
        .mockResolvedValueOnce(mockCategory2);
      habitCategoryRepository.reorderCategories.mockResolvedValue(undefined);

      await service.reorderCategories(categoryOrders);

      expect(habitCategoryRepository.findById).toHaveBeenCalledTimes(2);
      expect(habitCategoryRepository.findById).toHaveBeenCalledWith(
        'category-1',
      );
      expect(habitCategoryRepository.findById).toHaveBeenCalledWith(
        'category-2',
      );
      expect(habitCategoryRepository.reorderCategories).toHaveBeenCalledWith(
        categoryOrders,
      );
    });

    it('should throw BusinessException when any category not found', async () => {
      habitCategoryRepository.findById
        .mockResolvedValueOnce(mockCategory)
        .mockResolvedValueOnce(null);

      await expect(service.reorderCategories(categoryOrders)).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_CATEGORY_NOT_FOUND,
          '习惯分类不存在: category-2',
          HttpStatus.NOT_FOUND,
        ),
      );
    });
  });

  describe('createBatch', () => {
    const batchCreateDto: CreateHabitCategoryDto[] = [
      {
        name: '分类1',
        icon: 'icon1',
        sort_order: 1,
      },
      {
        name: '分类2',
        icon: 'icon2',
        sort_order: 2,
      },
    ];

    it('should create batch categories successfully', async () => {
      habitCategoryRepository.findByName
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(null);
      habitCategoryRepository.getNextSortOrder.mockResolvedValue(10);
      habitCategoryRepository.createCategory
        .mockResolvedValueOnce({
          ...mockCategory,
          name: '分类1',
          sort_order: 1,
        })
        .mockResolvedValueOnce({
          ...mockCategory2,
          name: '分类2',
          sort_order: 2,
        });

      const result = await service.createBatch(batchCreateDto);

      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('分类1');
      expect(result[1].name).toBe('分类2');
      expect(habitCategoryRepository.createCategory).toHaveBeenCalledTimes(2);
    });

    it('should return empty array when input is empty', async () => {
      const result = await service.createBatch([]);
      expect(result).toEqual([]);
    });

    it('should throw BusinessException when any name already exists', async () => {
      habitCategoryRepository.findByName
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(mockCategory);

      await expect(service.createBatch(batchCreateDto)).rejects.toThrow(
        new BusinessException(
          ErrorCode.HABIT_CATEGORY_NAME_EXISTS,
          '分类名称已存在: 分类2',
          HttpStatus.CONFLICT,
        ),
      );
    });
  });

  describe('getCategoryStats', () => {
    it('should return categories with template count', async () => {
      habitCategoryRepository.findActiveCategories.mockResolvedValue([
        mockCategory,
        mockCategory2,
      ]);

      const result = await service.getCategoryStats();

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe(mockCategory.id);
      expect(result[0].template_count).toBe(0); // Currently hardcoded to 0
      expect(result[1].id).toBe(mockCategory2.id);
      expect(result[1].template_count).toBe(0); // Currently hardcoded to 0
    });
  });
});

import { Injectable, HttpStatus } from '@nestjs/common';
import { HabitCategoryRepository } from '../repositories/habit-category.repository';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';
import {
  IHabitCategory,
  ICreateHabitCategory,
  IUpdateHabitCategory,
  IHabitCategoryQueryOptions,
  IHabitCategoryTree,
} from '../interfaces/habit-category.interface';
import { CreateHabitCategoryDto } from '../dto/habit-category/create-habit-category.dto';
import { UpdateHabitCategoryDto } from '../dto/habit-category/update-habit-category.dto';
import { HabitCategoryResponseDto } from '../dto/habit-category/habit-category-response.dto';
import { HabitCategoryQueryDto } from '../dto/habit-category/habit-category-query.dto';

@Injectable()
export class HabitCategoryService {
  constructor(
    private readonly habitCategoryRepository: HabitCategoryRepository,
  ) {}

  /**
   * 获取所有习惯分类
   */
  async findAll(
    query: HabitCategoryQueryDto,
  ): Promise<HabitCategoryResponseDto[]> {
    const queryOptions: IHabitCategoryQueryOptions = {
      is_active: query.is_active,
      level: query.level,
      parent_id: query.parent_id,
    };

    const categories =
      await this.habitCategoryRepository.findCategories(queryOptions);
    return categories.map((category) => this.mapToResponseDto(category));
  }

  /**
   * 获取分类树形结构
   */
  async findCategoryTree(
    query: HabitCategoryQueryDto,
  ): Promise<IHabitCategoryTree[]> {
    const queryOptions: IHabitCategoryQueryOptions = {
      is_active: query.is_active,
    };

    return this.habitCategoryRepository.findCategoryTree(queryOptions);
  }

  /**
   * 获取一级分类列表
   */
  async findTopLevelCategories(
    isActive?: boolean,
  ): Promise<HabitCategoryResponseDto[]> {
    const categories =
      await this.habitCategoryRepository.findTopLevelCategories(isActive);
    return categories.map((category) => this.mapToResponseDto(category));
  }

  /**
   * 获取子分类列表
   */
  async findChildren(parentId: string): Promise<HabitCategoryResponseDto[]> {
    const categories =
      await this.habitCategoryRepository.findChildren(parentId);
    return categories.map((category) => this.mapToResponseDto(category));
  }

  /**
   * 根据ID获取习惯分类
   */
  async findById(id: string): Promise<HabitCategoryResponseDto> {
    const category = await this.habitCategoryRepository.findById(id);

    if (!category) {
      throw new BusinessException(
        ErrorCode.HABIT_CATEGORY_NOT_FOUND,
        '习惯分类不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    return this.mapToResponseDto(category);
  }

  /**
   * 创建习惯分类
   */
  async create(
    createDto: CreateHabitCategoryDto,
  ): Promise<HabitCategoryResponseDto> {
    // 验证层级结构
    if (createDto.level < 1 || createDto.level > 2) {
      throw new BusinessException(
        ErrorCode.HABIT_CATEGORY_LEVEL_INVALID,
        '分类层级只能是1或2',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 验证一级分类不能有父分类
    if (createDto.level === 1 && createDto.parent_id) {
      throw new BusinessException(
        ErrorCode.HABIT_CATEGORY_LEVEL_INVALID,
        '一级分类不能有父分类',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 验证二级分类必须有父分类
    if (createDto.level === 2 && !createDto.parent_id) {
      throw new BusinessException(
        ErrorCode.HABIT_CATEGORY_LEVEL_INVALID,
        '二级分类必须指定父分类',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 如果是二级分类，验证父分类是否存在且为一级分类
    if (createDto.level === 2 && createDto.parent_id) {
      const isValidParent =
        await this.habitCategoryRepository.validateParentCategory(
          createDto.parent_id,
        );
      if (!isValidParent) {
        throw new BusinessException(
          ErrorCode.HABIT_CATEGORY_PARENT_INVALID,
          '父分类不存在或不是一级分类',
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    // 检查分类名称是否在同一层级下已存在
    const existingCategory = await this.habitCategoryRepository.findByName(
      createDto.name,
      createDto.parent_id,
    );

    if (existingCategory) {
      throw new BusinessException(
        ErrorCode.HABIT_CATEGORY_NAME_EXISTS,
        '分类名称在同一层级下已存在',
        HttpStatus.CONFLICT,
      );
    }

    // 如果没有指定排序顺序，获取下一个排序顺序
    let sortOrder = createDto.sort_order;
    if (sortOrder === undefined) {
      sortOrder = await this.habitCategoryRepository.getNextSortOrder(
        createDto.level,
        createDto.parent_id,
      );
    }

    const createData: ICreateHabitCategory = {
      name: createDto.name,
      icon: createDto.icon,
      parent_id: createDto.parent_id,
      level: createDto.level,
      sort_order: sortOrder,
      is_active: createDto.is_active ?? true,
    };

    const category =
      await this.habitCategoryRepository.createCategory(createData);
    return this.mapToResponseDto(category);
  }

  /**
   * 更新习惯分类
   */
  async update(
    id: string,
    updateDto: UpdateHabitCategoryDto,
  ): Promise<HabitCategoryResponseDto> {
    // 验证分类是否存在
    const existingCategory = await this.habitCategoryRepository.findById(id);
    if (!existingCategory) {
      throw new BusinessException(
        ErrorCode.HABIT_CATEGORY_NOT_FOUND,
        '习惯分类不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    // 如果更新名称，检查是否在同一层级下重复
    if (updateDto.name && updateDto.name !== existingCategory.name) {
      const duplicateCategory = await this.habitCategoryRepository.findByName(
        updateDto.name,
        existingCategory.parent_id,
      );

      if (duplicateCategory && duplicateCategory.id !== id) {
        throw new BusinessException(
          ErrorCode.HABIT_CATEGORY_NAME_EXISTS,
          '分类名称在同一层级下已存在',
          HttpStatus.CONFLICT,
        );
      }
    }

    const updateData: IUpdateHabitCategory = {
      name: updateDto.name,
      icon: updateDto.icon,
      sort_order: updateDto.sort_order,
      is_active: updateDto.is_active,
    };

    const category = await this.habitCategoryRepository.updateCategory(
      id,
      updateData,
    );
    return this.mapToResponseDto(category);
  }

  /**
   * 删除习惯分类
   */
  async delete(id: string): Promise<void> {
    const category = await this.habitCategoryRepository.findById(id);
    if (!category) {
      throw new BusinessException(
        ErrorCode.HABIT_CATEGORY_NOT_FOUND,
        '习惯分类不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    // 检查分类下是否有子分类
    const hasChildren = await this.habitCategoryRepository.hasChildren(id);
    if (hasChildren) {
      throw new BusinessException(
        ErrorCode.HABIT_CATEGORY_HAS_CHILDREN,
        '分类下存在子分类，无法删除',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 检查分类下是否有习惯模板
    const hasTemplates = await this.habitCategoryRepository.hasTemplates(id);
    if (hasTemplates) {
      throw new BusinessException(
        ErrorCode.HABIT_CATEGORY_HAS_TEMPLATES,
        '分类下存在习惯模板，无法删除',
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.habitCategoryRepository.delete(id);
  }

  /**
   * 激活分类
   */
  async activate(id: string): Promise<HabitCategoryResponseDto> {
    const category = await this.habitCategoryRepository.findById(id);
    if (!category) {
      throw new BusinessException(
        ErrorCode.HABIT_CATEGORY_NOT_FOUND,
        '习惯分类不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    const updatedCategory = await this.habitCategoryRepository.updateCategory(
      id,
      { is_active: true },
    );
    return this.mapToResponseDto(updatedCategory);
  }

  /**
   * 停用分类
   */
  async deactivate(id: string): Promise<HabitCategoryResponseDto> {
    const category = await this.habitCategoryRepository.findById(id);
    if (!category) {
      throw new BusinessException(
        ErrorCode.HABIT_CATEGORY_NOT_FOUND,
        '习惯分类不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    const updatedCategory = await this.habitCategoryRepository.updateCategory(
      id,
      { is_active: false },
    );
    return this.mapToResponseDto(updatedCategory);
  }

  /**
   * 重新排序分类
   */
  async reorderCategories(
    categoryOrders: { id: string; sort_order: number }[],
  ): Promise<void> {
    // 验证所有分类是否存在
    for (const { id } of categoryOrders) {
      const category = await this.habitCategoryRepository.findById(id);
      if (!category) {
        throw new BusinessException(
          ErrorCode.HABIT_CATEGORY_NOT_FOUND,
          `习惯分类不存在: ${id}`,
          HttpStatus.NOT_FOUND,
        );
      }
    }

    await this.habitCategoryRepository.reorderCategories(categoryOrders);
  }

  /**
   * 批量创建分类
   */
  async createBatch(
    categories: CreateHabitCategoryDto[],
  ): Promise<HabitCategoryResponseDto[]> {
    if (!categories || categories.length === 0) {
      return [];
    }

    // 检查名称重复和层级验证
    for (const category of categories) {
      // 验证层级
      if (category.level < 1 || category.level > 2) {
        throw new BusinessException(
          ErrorCode.HABIT_CATEGORY_LEVEL_INVALID,
          `分类层级只能是1或2: ${category.name}`,
          HttpStatus.BAD_REQUEST,
        );
      }

      // 验证父分类关系
      if (category.level === 1 && category.parent_id) {
        throw new BusinessException(
          ErrorCode.HABIT_CATEGORY_LEVEL_INVALID,
          `一级分类不能有父分类: ${category.name}`,
          HttpStatus.BAD_REQUEST,
        );
      }

      if (category.level === 2 && !category.parent_id) {
        throw new BusinessException(
          ErrorCode.HABIT_CATEGORY_LEVEL_INVALID,
          `二级分类必须指定父分类: ${category.name}`,
          HttpStatus.BAD_REQUEST,
        );
      }

      // 检查名称重复
      const existing = await this.habitCategoryRepository.findByName(
        category.name,
        category.parent_id,
      );
      if (existing) {
        throw new BusinessException(
          ErrorCode.HABIT_CATEGORY_NAME_EXISTS,
          `分类名称在同一层级下已存在: ${category.name}`,
          HttpStatus.CONFLICT,
        );
      }
    }

    const results: HabitCategoryResponseDto[] = [];

    for (const categoryDto of categories) {
      // 获取排序顺序
      let sortOrder = categoryDto.sort_order;
      if (sortOrder === undefined) {
        sortOrder = await this.habitCategoryRepository.getNextSortOrder(
          categoryDto.level,
          categoryDto.parent_id,
        );
      }

      const createData: ICreateHabitCategory = {
        name: categoryDto.name,
        icon: categoryDto.icon,
        parent_id: categoryDto.parent_id,
        level: categoryDto.level,
        sort_order: sortOrder,
        is_active: categoryDto.is_active ?? true,
      };

      const category =
        await this.habitCategoryRepository.createCategory(createData);
      results.push(this.mapToResponseDto(category));
    }

    return results;
  }

  /**
   * 获取分类统计信息（包含模板数量）
   */
  async getCategoryStats(): Promise<
    (HabitCategoryResponseDto & { template_count: number })[]
  > {
    const categories =
      await this.habitCategoryRepository.findActiveCategories();

    const categoriesWithStats = await Promise.all(
      categories.map(async (category) => {
        // 这里需要通过 HabitTemplateRepository 获取模板数量
        // 但为了避免循环依赖，我们暂时返回基本信息
        // 实际实现中可能需要在 repository 层添加统计方法
        const responseDto = this.mapToResponseDto(category);
        return {
          ...responseDto,
          template_count: 0, // 待实现：获取实际模板数量
        };
      }),
    );

    return categoriesWithStats;
  }

  /**
   * 将实体映射为响应DTO
   */
  private mapToResponseDto(category: IHabitCategory): HabitCategoryResponseDto {
    const dto = new HabitCategoryResponseDto();
    dto.id = category.id;
    dto.name = category.name;
    dto.icon = category.icon;
    dto.parent_id = category.parent_id;
    dto.level = category.level;
    dto.sort_order = category.sort_order;
    dto.is_active = category.is_active;
    dto.created_at = category.created_at;
    dto.updated_at = category.updated_at;

    return dto;
  }
}

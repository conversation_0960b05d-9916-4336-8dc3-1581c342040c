import { Test, TestingModule } from '@nestjs/testing';
import { AdminHabitCategoryController } from '../admin-habit-category.controller';
import { HabitCategoryService } from '../../../services/habit-category.service';
import { CreateHabitCategoryDto } from '../../../dto/habit-category/create-habit-category.dto';
import { UpdateHabitCategoryDto } from '../../../dto/habit-category/update-habit-category.dto';
import { HabitCategoryResponseDto } from '../../../dto/habit-category/habit-category-response.dto';
import { HabitCategoryQueryDto } from '../../../dto/habit-category/habit-category-query.dto';

describe('AdminHabitCategoryController', () => {
  let controller: AdminHabitCategoryController;
  let service: HabitCategoryService;

  const mockHabitCategoryService = {
    findAll: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    activate: jest.fn(),
    deactivate: jest.fn(),
    createBatch: jest.fn(),
    reorderCategories: jest.fn(),
    getCategoryStats: jest.fn(),
  };

  const mockCategory: HabitCategoryResponseDto = {
    id: 'category-1',
    name: '生活自理',
    icon: 'life-care-icon',
    sort_order: 1,
    is_active: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminHabitCategoryController],
      providers: [
        {
          provide: HabitCategoryService,
          useValue: mockHabitCategoryService,
        },
      ],
    }).compile();

    controller = module.get<AdminHabitCategoryController>(
      AdminHabitCategoryController,
    );
    service = module.get<HabitCategoryService>(HabitCategoryService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all habit categories', async () => {
      const query: HabitCategoryQueryDto = {};
      const expectedResult = [mockCategory];

      mockHabitCategoryService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll(query);

      expect(result).toEqual(expectedResult);
      expect(service.findAll).toHaveBeenCalledWith(query);
    });

    it('should return categories with query filters', async () => {
      const query: HabitCategoryQueryDto = {
        is_active: true,
        limit: 10,
        offset: 0,
      };
      const expectedResult = [mockCategory];

      mockHabitCategoryService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll(query);

      expect(result).toEqual(expectedResult);
      expect(service.findAll).toHaveBeenCalledWith(query);
    });
  });

  describe('findById', () => {
    it('should return a habit category by id', async () => {
      const id = 'category-1';

      mockHabitCategoryService.findById.mockResolvedValue(mockCategory);

      const result = await controller.findById(id);

      expect(result).toEqual(mockCategory);
      expect(service.findById).toHaveBeenCalledWith(id);
    });
  });

  describe('create', () => {
    it('should create a new habit category', async () => {
      const createDto: CreateHabitCategoryDto = {
        name: '生活自理',
        icon: 'life-care-icon',
        sort_order: 1,
        is_active: true,
      };

      mockHabitCategoryService.create.mockResolvedValue(mockCategory);

      const result = await controller.create(createDto);

      expect(result).toEqual(mockCategory);
      expect(service.create).toHaveBeenCalledWith(createDto);
    });
  });

  describe('update', () => {
    it('should update a habit category', async () => {
      const id = 'category-1';
      const updateDto: UpdateHabitCategoryDto = {
        name: '生活自理（更新）',
        sort_order: 2,
      };
      const updatedCategory = { ...mockCategory, ...updateDto };

      mockHabitCategoryService.update.mockResolvedValue(updatedCategory);

      const result = await controller.update(id, updateDto);

      expect(result).toEqual(updatedCategory);
      expect(service.update).toHaveBeenCalledWith(id, updateDto);
    });
  });

  describe('delete', () => {
    it('should delete a habit category', async () => {
      const id = 'category-1';

      mockHabitCategoryService.delete.mockResolvedValue(undefined);

      const result = await controller.delete(id);

      expect(result).toBeUndefined();
      expect(service.delete).toHaveBeenCalledWith(id);
    });
  });

  describe('activate', () => {
    it('should activate a habit category', async () => {
      const id = 'category-1';
      const activatedCategory = { ...mockCategory, is_active: true };

      mockHabitCategoryService.activate.mockResolvedValue(activatedCategory);

      const result = await controller.activate(id);

      expect(result).toEqual(activatedCategory);
      expect(service.activate).toHaveBeenCalledWith(id);
    });
  });

  describe('deactivate', () => {
    it('should deactivate a habit category', async () => {
      const id = 'category-1';
      const deactivatedCategory = { ...mockCategory, is_active: false };

      mockHabitCategoryService.deactivate.mockResolvedValue(
        deactivatedCategory,
      );

      const result = await controller.deactivate(id);

      expect(result).toEqual(deactivatedCategory);
      expect(service.deactivate).toHaveBeenCalledWith(id);
    });
  });

  describe('createBatch', () => {
    it('should create multiple habit categories', async () => {
      const categories: CreateHabitCategoryDto[] = [
        {
          name: '生活自理',
          icon: 'life-care-icon',
          sort_order: 1,
        },
        {
          name: '健康习惯',
          icon: 'health-icon',
          sort_order: 2,
        },
      ];
      const expectedResult = [
        mockCategory,
        { ...mockCategory, id: 'category-2', name: '健康习惯', sort_order: 2 },
      ];

      mockHabitCategoryService.createBatch.mockResolvedValue(expectedResult);

      const result = await controller.createBatch(categories);

      expect(result).toEqual(expectedResult);
      expect(service.createBatch).toHaveBeenCalledWith(categories);
    });
  });

  describe('reorderCategories', () => {
    it('should reorder categories', async () => {
      const categoryOrders = [
        { id: 'category-1', sort_order: 2 },
        { id: 'category-2', sort_order: 1 },
      ];

      mockHabitCategoryService.reorderCategories.mockResolvedValue(undefined);

      const result = await controller.reorderCategories(categoryOrders);

      expect(result).toBeUndefined();
      expect(service.reorderCategories).toHaveBeenCalledWith(categoryOrders);
    });
  });

  describe('getCategoryStats', () => {
    it('should return category statistics', async () => {
      const expectedResult = [{ ...mockCategory, template_count: 5 }];

      mockHabitCategoryService.getCategoryStats.mockResolvedValue(
        expectedResult,
      );

      const result = await controller.getCategoryStats();

      expect(result).toEqual(expectedResult);
      expect(service.getCategoryStats).toHaveBeenCalled();
    });
  });
});
